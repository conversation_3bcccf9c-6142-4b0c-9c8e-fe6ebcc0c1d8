name: Build and Publish

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'
        required: true
        default: 'warning'
        type: choice
        options:
          - info
          - warning
          - debug

permissions:
  contents: read

jobs:
  build:
    name: Build package
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.x'
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build
      - name: Build package
        run: python -m build
      - name: Archive production artifacts
        uses: actions/upload-artifact@v4.6.2
        with:
          name: dist
          path: dist

  pypi-publish:
    name: Upload release to <PERSON>y<PERSON>
    needs: build
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/p/gemini-webapi
    permissions:
      id-token: write  # IMPORTANT: this permission is mandatory for trusted publishing
    steps:
      - name: Retrieve built artifacts
        uses: actions/download-artifact@v4.3.0
        with:
          name: dist
          path: dist
      - name: Publish package distributions to PyPI
        uses: pypa/gh-action-pypi-publish@v1.12.4

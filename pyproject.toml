[build-system]
requires = ["setuptools>=69", "setuptools_scm>=8"]
build-backend = "setuptools.build_meta"

[project]
name = "gemini-webapi"
authors = [
    {name = "UZQueen"},
]
description = "✨ An elegant async Python wrapper for Google Gemini web app"
readme = "README.md"
license = {file = "LICENSE"}
keywords = ["API", "async", "Gemini", "Bard", "Google", "Generative AI", "LLM"]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.10"
dependencies = [
    "httpx[http2]~=0.28.1",
    "loguru~=0.7.3",
    "pydantic~=2.11.3",
]
dynamic = ["version"]

[project.urls]
Repository = "https://github.com/HanaokaYuzu/Gemini-API"
Issues = "https://github.com/HanaokaYuzu/Gemini-API/issues"

[tool.setuptools.packages.find]
where = ["src"]
include = ["gemini_webapi"]

[tool.setuptools_scm]
version_scheme = "post-release"
local_scheme = "no-local-version"

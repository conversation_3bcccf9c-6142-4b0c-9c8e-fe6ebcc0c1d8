import asyncio
import os
from typing import Dict, List, Optional, Union, Literal

from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from gemini_webapi import Gemini<PERSON><PERSON>
from gemini_webapi.constants import Model

# Initialize FastAPI app
app = FastAPI(title="Gemini OpenAI-compatible API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global client instances
clients = {}
client_locks = {}

# Get environment variables
GEMINI_PSID_1 = os.getenv("GEMINI_PSID_1", "")
GEMINI_PSIDTS_1 = os.getenv("GEMINI_PSIDTS_1", "")
GEMINI_PSID_2 = os.getenv("GEMINI_PSID_2", "")
GEMINI_PSIDTS_2 = os.getenv("GEMINI_PSIDTS_2", "")
PROXY_URL = os.getenv("PROXY_URL", None)

# Model mapping from OpenAI to Gemini
MODEL_MAPPING = {
    "gpt-3.5-turbo": Model.G_2_0_FLASH,
    "gpt-4": Model.G_2_5_PRO,
    "gpt-4-turbo": Model.G_2_5_PRO,
}

# Pydantic models for API
class Message(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[Message]
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = None
    stream: Optional[bool] = False

class Choice(BaseModel):
    index: int
    message: Message
    finish_reason: str = "stop"

class Usage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int

class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[Choice]
    usage: Usage

class ModelData(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str

class ModelList(BaseModel):
    object: str = "list"
    data: List[ModelData]

# Helper functions
async def get_client(account_num: int = 1):
    """Get or create a Gemini client for the specified account."""
    global clients, client_locks
    
    if account_num not in client_locks:
        client_locks[account_num] = asyncio.Lock()
    
    async with client_locks[account_num]:
        if account_num not in clients or clients[account_num] is None:
            if account_num == 1:
                psid = GEMINI_PSID_1
                psidts = GEMINI_PSIDTS_1
            else:
                psid = GEMINI_PSID_2
                psidts = GEMINI_PSIDTS_2
                
            if not psid:
                raise HTTPException(status_code=500, detail=f"GEMINI_PSID_{account_num} not configured")
                
            client = GeminiClient(psid, psidts, proxy=PROXY_URL)
            await client.init(auto_refresh=True)
            clients[account_num] = client
            
    return clients[account_num]

# Routes
@app.get("/v1/models")
async def list_models():
    """List available models in OpenAI-compatible format."""
    import time
    current_time = int(time.time())
    
    return {
        "object": "list",
        "data": [
            {"id": "gpt-3.5-turbo", "object": "model", "created": **********, "owned_by": "openai"},
            {"id": "gpt-4", "object": "model", "created": **********, "owned_by": "openai"},
            {"id": "gpt-4-turbo", "object": "model", "created": **********, "owned_by": "openai"},
        ],
    }

@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    """Create a chat completion using Gemini API."""
    import time
    import uuid
    
    # Get the appropriate Gemini model
    gemini_model = MODEL_MAPPING.get(request.model)
    if not gemini_model:
        raise HTTPException(status_code=400, detail=f"Model {request.model} not supported")
    
    # Prepare conversation history
    conversation = []
    for msg in request.messages:
        if msg.role == "system":
            # Add system message as a user message with special formatting
            conversation.append({"role": "user", "content": f"System instruction: {msg.content}"})
        else:
            conversation.append({"role": msg.role, "content": msg.content})
    
    # Get client (alternating between accounts for load balancing)
    account_num = 1 if len(conversation) % 2 == 0 else 2
    try:
        client = await get_client(account_num)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to initialize Gemini client: {str(e)}")
    
    # Start a chat session
    chat = client.start_chat(model=gemini_model.model_name)
    
    # Send all messages except the last one to build conversation history
    last_msg = None
    for i, msg in enumerate(conversation):
        if i == len(conversation) - 1:
            last_msg = msg
            break
        
        if msg["role"] == "user":
            await chat.send_message(msg["content"])
        # Skip assistant messages as they're handled by the API
    
    # Send the final message and get the response
    try:
        if last_msg and last_msg["role"] == "user":
            response = await chat.send_message(last_msg["content"])
        else:
            # If there's no user message at the end, we can't get a response
            raise HTTPException(status_code=400, detail="The last message must be from the user")
        
        # Create OpenAI-compatible response
        completion_response = ChatCompletionResponse(
            id=f"chatcmpl-{uuid.uuid4()}",
            created=int(time.time()),
            model=request.model,
            choices=[
                Choice(
                    index=0,
                    message=Message(role="assistant", content=response.text),
                    finish_reason="stop"
                )
            ],
            usage=Usage(
                prompt_tokens=len(str(request.messages)) // 4,  # Rough estimate
                completion_tokens=len(response.text) // 4,      # Rough estimate
                total_tokens=(len(str(request.messages)) + len(response.text)) // 4
            )
        )
        
        return completion_response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")

@app.get("/")
async def root():
    return {"message": "Gemini OpenAI-compatible API is running. Use /v1/chat/completions endpoint."}

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=50014, reload=True)
